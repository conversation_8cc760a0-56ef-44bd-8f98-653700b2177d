/**
 * Shared Components - Centralized exports
 * These components eliminate massive code duplication across video pages
 */

// Layout components
export {
  VideoPageLayout,
  VideoTwoColumnLayout,
  FormSection,
  PreviewSection,
  CompleteVideoPageLayout
} from './VideoPageLayout';

// Button components
export {
  GenerationButton,
  AIVideoGenerationButton,
  MemeVideoGenerationButton,
  PodcastClipperButton,
  AIUGCGenerationButton,
  RedditPostGenerationButton,
  TwitterPostGenerationButton,
  StockMediaGenerationButton,
  NarratorGenerationButton
} from './GenerationButton';

// Form components
export { FormSection, FormSectionGroup, FormSectionHeader } from './FormSection';

// Selector components
export { OptionSelector } from './OptionSelector';

// Preview components
export { VideoPreviewPanel } from './VideoPreviewPanel';
